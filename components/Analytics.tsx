// 'use client';

// import Script from 'next/script';

// export function Analytics() {
//   return (
//     <>
//       <Script
//         src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`}
//         strategy="afterInteractive"
//       />
//       <Script id="google-analytics" strategy="afterInteractive">
//         {`
//           window.dataLayer = window.dataLayer || [];
//           function gtag(){dataLayer.push(arguments);}
//           gtag('js', new Date());
//           gtag('config', '${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}', {
//             page_path: window.location.pathname,
//           });
//         `}
//       </Script>
//     </>
//   );
// }