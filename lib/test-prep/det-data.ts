import { BookOpen, Users, Trophy, Calendar, Clock, Star } from 'lucide-react';

export const features = [
  {
    title: 'Adaptive Test Practice',
    description: 'Train with our AI-powered adaptive practice system'
  },
  {
    title: 'Video Interview Prep',
    description: 'Expert guidance for video interview tasks'
  },
  {
    title: 'Small Batch Size',
    description: 'Maximum 6 students for personalized attention'
  },
  {
    title: 'Mock Tests',
    description: 'Regular practice with real test conditions'
  },
  {
    title: 'Quick Results',
    description: 'Practice tests with instant scoring'
  },
  {
    title: 'Flexible Learning',
    description: 'Online and hybrid learning options'
  }
];

export const scheduleItems = [
  {
    title: 'Duration',
    detail: '4 weeks intensive program',
    icon: Clock
  },
  {
    title: 'Batch Size',
    detail: 'Maximum 6 students',
    icon: Users
  },
  {
    title: 'Schedule',
    detail: 'Flexible online sessions',
    icon: Calendar
  }
];