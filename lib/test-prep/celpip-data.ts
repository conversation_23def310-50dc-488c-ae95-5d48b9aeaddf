import { BookOpen, Users, Trophy, Calendar, Clock, Star } from 'lucide-react';

export const features = [
  {
    title: 'Canadian English Focus',
    description: 'Learn from experts in Canadian English and culture'
  },
  {
    title: 'Computer-Based Practice',
    description: 'Train on computer-delivered test format'
  },
  {
    title: 'Small Batch Size',
    description: 'Maximum 10 students for personalized attention'
  },
  {
    title: 'Mock Tests',
    description: 'Regular practice with Canadian contexts'
  },
  {
    title: 'Speaking Practice',
    description: 'One-on-one speaking sessions with feedback'
  },
  {
    title: 'Writing Evaluation',
    description: 'Detailed feedback on writing tasks'
  }
];

export const scheduleItems = [
  {
    title: 'Duration',
    detail: '6 weeks program',
    icon: Clock
  },
  {
    title: 'Batch Size',
    detail: 'Maximum 10 students',
    icon: Users
  },
  {
    title: 'Schedule',
    detail: 'Evening & weekend batches',
    icon: Calendar
  }
];