import { BookOpen, Users, Trophy } from 'lucide-react';

export const features = [
  {
    title: 'Proven Track Record',
    description: 'Numerous students scoring 7+ and 8+ bands'
  },
  {
    title: 'Comprehensive Materials',
    description: 'Access to complete study guides and resources'
  },
  {
    title: 'Certified Trainers',
    description: 'Experienced instructors dedicated to your success'
  },
  {
    title: 'Regular Mock Tests',
    description: 'Scheduled tests with detailed evaluations'
  },
  {
    title: 'Flexible Timings',
    description: 'Online and offline sessions available'
  },
  {
    title: 'Personalized Attention',
    description: 'Smaller class sizes for one-on-one support'
  }
];

export const scheduleItems = [
  {
    title: 'Duration',
    detail: '8 weeks program',
    icon: BookOpen
  },
  {
    title: 'Batch Size',
    detail: 'Maximum 10 students',
    icon: Users
  },
  {
    title: 'Study Material',
    detail: 'Comprehensive package included',
    icon: BookOpen
  }
];