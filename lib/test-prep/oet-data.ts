import { BookOpen, Users, Trophy, Calendar, Clock, Star } from 'lucide-react';

export const features = [
  {
    title: 'Healthcare Experts',
    description: 'Learn from instructors with medical background'
  },
  {
    title: 'Specialized Materials',
    description: 'Healthcare-specific study resources and practice tests'
  },
  {
    title: 'Small Groups',
    description: 'Maximum 8 students per batch for personalized attention'
  },
  {
    title: 'Role-play Practice',
    description: 'Extensive practice with medical scenarios'
  },
  {
    title: 'Writing Workshops',
    description: 'Focused training on medical documentation'
  },
  {
    title: 'Mock Tests',
    description: 'Regular assessments with detailed feedback'
  }
];

export const scheduleItems = [
  {
    title: 'Duration',
    detail: '10 weeks program',
    icon: Clock
  },
  {
    title: 'Batch Size',
    detail: 'Maximum 8 students',
    icon: Users
  },
  {
    title: 'Schedule',
    detail: 'Weekend & evening batches',
    icon: Calendar
  }
];