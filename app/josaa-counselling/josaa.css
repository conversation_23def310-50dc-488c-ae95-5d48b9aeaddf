/* JoSAA Counselling specific animations */
.animate-item {
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-item.animate-in {
  opacity: 1 !important;
  transform: translateY(0) translateX(0) !important;
}

@media (prefers-reduced-motion: reduce) {
  .animate-item {
    transition: none;
  }
}

/* Custom animations for floating elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes float-reverse {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(10px);
  }
}

@keyframes scale-down {
  from {
    transform: scaleY(0) translateX(-50%);
  }
  to {
    transform: scaleY(1) translateX(-50%);
  }
}

.animate-scale-down {
  animation: scale-down 1s ease-out forwards;
  animation-delay: 0.3s;
}

/* Responsive animation control */
@media (prefers-reduced-motion: reduce) {
  .animate-scale-down {
    animation: none;
    transform: scaleY(1) translateX(-50%);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 5s ease-in-out infinite;
}

/* Cross-browser compatibility fixes for JoSAA page */
.josaa-main {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* Firefox specific */
@-moz-document url-prefix() {
  .josaa-main section:first-child {
    padding-top: 5rem !important;
  }
}

/* Edge and Safari specific via feature detection */
@supports (-ms-ime-align:auto) or (-webkit-touch-callout:inherit) {
  .josaa-main section:first-child {
    padding-top: 5rem !important;
    margin-top: 0 !important;
  }
}

/* Specific override for the JoSAA page to ensure consistent spacing across browsers */
.josaa-main-override section:first-child {
  /* These values are carefully chosen to work well across Chrome, Firefox, Edge and Safari */
  padding-top: 5rem !important;
  padding-bottom: 2rem !important;
  margin-top: 0;
  box-sizing: border-box;
}

/* Mobile adjustments for the override */
@media (max-width: 767px) {
  .josaa-main-override section:first-child {
    padding-top: 4rem !important;
  }
}

/* Fix for empty space at top in mobile view */
@media (max-width: 768px) {
  .josaa-main section:first-child {
    padding-top: 4rem !important; /* 64px, reduced to account for smaller navbar in mobile */
  }
}

/* Ensure proper sizing for the hero section */
@media (max-width: 768px) {
  .josaa-main {
    margin-top: 0 !important;
  }
  
  /* Avoid extra space at the top of the page */
  header + main.josaa-main {
    padding-top: 0 !important;
  }
}
