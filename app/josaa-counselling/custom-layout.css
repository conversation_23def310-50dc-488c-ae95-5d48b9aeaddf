/* Custom layout adjustments for JoSAA page - Cross-browser compatible */
body > main.josaa-main {
  padding-top: 0 !important;
  margin-top: 0 !important;
  padding-bottom: 0 !important;
}

/* Cross-browser padding fix for hero section */
.josaa-main section:first-child {
  /* Standard spacing that works across browsers */
  padding-top: 5rem !important; /* Safe padding that works on both Chrome and Edge */
  margin-top: -1rem; /* Negative margin to adjust for browser inconsistencies */
}

/* Mobile-specific adjustments */
@media (max-width: 767px) {
  body {
    --navigation-height: 4rem; /* 64px navbar height for mobile */
  }
  
  /* Fix for mobile view */
  .josaa-main section:first-child {
    padding-top: 4rem !important; /* 64px - matches navbar height on mobile */
  }
}

@media (min-width: 768px) {
  body {
    --navigation-height: 5rem; /* 80px navbar height for desktop */
  }
}
